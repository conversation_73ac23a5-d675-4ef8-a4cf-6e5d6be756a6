#!/usr/bin/env python3
"""
Test script to verify the daily balance color coding functionality.
This script tests the Excel generation with color coding for different scenarios.
"""

import os
import sys
import json
from datetime import datetime
from src.excel import process_json_and_generate_report

def test_color_coding():
    """Test the color coding functionality with the current data."""
    
    print("🧪 Testing Daily Balance Color Coding")
    print("=" * 50)
    
    # Test with the current data directory
    test_dir = r"D:\Projects\USBankDetailExtraction\Data1\20250624_133122_49358ce8"
    
    if not os.path.exists(test_dir):
        print(f"❌ Test directory not found: {test_dir}")
        return False
    
    try:
        print(f"📁 Testing with directory: {test_dir}")
        
        # Generate Excel report with color coding
        output_file = process_json_and_generate_report(test_dir)
        
        if output_file and os.path.exists(output_file):
            print(f"✅ Excel file generated successfully: {output_file}")
            
            # Check if the file has content
            file_size = os.path.getsize(output_file)
            print(f"📊 File size: {file_size:,} bytes")
            
            if file_size > 5000:  # Should be at least 5KB for a proper Excel file
                print("✅ File appears to have proper content")
                return True
            else:
                print("⚠️ File seems too small, might be incomplete")
                return False
        else:
            print("❌ Excel file was not generated")
            return False
            
    except Exception as e:
        print(f"❌ Error during testing: {e}")
        return False

def print_summary():
    """Print a summary of what the color coding feature does."""
    
    print("\n" + "=" * 60)
    print("📋 DAILY BALANCE COLOR CODING SUMMARY")
    print("=" * 60)
    
    print("""
🎯 FEATURE OVERVIEW:
   • Compares extracted daily ending balances with calculated balances
   • Colors Excel rows GREEN when balances match
   • Colors Excel rows RED when balances don't match
   • Uses a tolerance of $0.01 for comparison

📊 HOW IT WORKS:
   1. Loads extracted daily balances from JSON files
   2. Calculates actual daily balances from transaction data
   3. Compares extracted vs calculated for each date
   4. Applies color coding to entire rows in Excel

🔍 VALIDATION LOGIC:
   • Starting Balance + Credits - Debits = Ending Balance
   • Tolerance: ±$0.01 for rounding differences
   • Green = Match, Red = Mismatch

📁 INPUT FILES:
   • EndingBalance_trueData.json (extracted balances)
   • CreditPages_trueData.json (credit transactions)
   • DebitPages_trueData.json (debit transactions)
   • CheckPages_trueData.json (check transactions)
   • OpeningPage_trueData.json (starting balance)

📈 OUTPUT:
   • Excel file with color-coded rows
   • Detailed logging of matches/mismatches
   • Balance comparison summary
    """)

if __name__ == "__main__":
    print_summary()
    
    # Run the test
    success = test_color_coding()
    
    if success:
        print("\n🎉 ALL TESTS PASSED!")
        print("The daily balance color coding feature is working correctly.")
        sys.exit(0)
    else:
        print("\n❌ TESTS FAILED!")
        print("There were issues with the color coding implementation.")
        sys.exit(1)
