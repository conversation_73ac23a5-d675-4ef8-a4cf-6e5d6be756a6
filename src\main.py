#!/usr/bin/env python3
"""
USB Bank Statement Processor - Main Entry Point

This script serves as the main entry point for the bank statement processing application.
It takes an input PDF path and processes it using the functionality in bankStatement.py.

Usage:
    python main.py <input_pdf_path>

Example:
    python main.py "statements/Jan_Statement.pdf"
"""

import os
import sys
import asyncio
import logging
import time
import traceback
from pathlib import Path
from typing import Optional

# Import the process_bank_statement function and setup_logging from bankStatement.py
from bankStatement import process_bank_statement, setup_logging

# Import the Excel report generation function
try:
    from excel import process_json_and_generate_report
except ImportError:
    process_json_and_generate_report = None
    print("Warning: Excel module not found. Excel report generation will be disabled.")

# Set up logging using the imported setup_logging function from bankStatement.py
logger = setup_logging()
logger.info(f"Logging initialized. Log file: {os.path.join('logs', os.path.basename(logger.handlers[0].baseFilename))}")


async def process_pdf(input_pdf_path: str) -> Optional[str]:
    """
    Process a bank statement PDF and return the results directory.

    Args:
        input_pdf_path: Path to the input PDF file

    Returns:
        Path to the results directory or None if processing failed
    """
    if not os.path.exists(input_pdf_path):
        logger.error(f"Input PDF file '{input_pdf_path}' does not exist")
        return None

    try:
        # Process the bank statement with concurrent JSON generation
        start_time = time.time()
        logger.info(f"Starting bank statement processing for: {input_pdf_path}")

        # Call the process_bank_statement function from bankStatement.py
        results_dir = await process_bank_statement(input_pdf_path)

        if not results_dir:
            logger.error("Bank statement processing failed")
            return None

        processing_time = time.time() - start_time
        logger.info(f"Bank statement processing completed in {processing_time:.2f} seconds")
        logger.info(f"Results saved to: {results_dir}")

        return results_dir

    except Exception as e:
        logger.error(f"Error processing PDF: {e}")
        logger.error(traceback.format_exc())
        return None


async def main():
    """Main entry point for the application."""
    # Test colored logging
    logger.info("This is an INFO message (normal color)")
    logger.warning("This is a WARNING message (should be orange/yellow)")
    logger.error("This is an ERROR message (should be red)")

    # Get input PDF path from command line arguments
    if len(sys.argv) > 1:
        input_pdf = sys.argv[1]
    else:
        # Default PDF path if none is provided
        input_pdf = r"C:\Users\<USER>\Downloads\test\Potosi Lumber 12.31.24.pdf"
        logger.info(f"No input PDF path provided. Using default: {input_pdf}")

    # Process the PDF and get the results directory
    results_dir = await process_pdf(input_pdf)

    if results_dir:
        logger.info(f"Processing completed successfully. Results in: {results_dir}")

        # Generate Excel report if the function is available
        if process_json_and_generate_report:
            try:
                excel_start_time = time.time()
                logger.info(f"Generating Excel report for: {results_dir}")
                process_json_and_generate_report(results_dir)
                excel_time = time.time() - excel_start_time
                logger.info(f"Excel report generated successfully in {excel_time:.2f} seconds")
            except Exception as e:
                logger.error(f"Failed to generate Excel report: {e}")
                logger.error(traceback.format_exc())
                # Continue execution even if Excel generation fails

        return 1  # Return 1 for success
    else:
        logger.error("Processing failed")
        return 0  # Return 0 for failure


if __name__ == "__main__":
    # Run the main function and get the exit code
    # Note: exit_code of 1 means success, 0 means failure (as per your requirement)
    exit_code = asyncio.run(main())

    # Print a final status message
    if exit_code == 1:
        print("✅ Processing completed successfully!")
    else:
        print("❌ Processing failed!")

    # Exit with the appropriate code
    # Note: In standard practice, 0 means success and non-zero means failure
    # But we're following the requirement where 1 means success
    sys.exit(0 if exit_code == 1 else 1)